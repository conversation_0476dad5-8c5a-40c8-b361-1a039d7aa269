import uuid
from fastapi.testclient import TestClient
from sqlmodel import Session, select

from app.core.config import settings
from app.models import DocumentCreate, Document
from app.tests.utils.utils import random_lower_string, random_email
from app.tests.utils.item import create_random_item

# 说明：
# - 使用现有的 fixtures: client, db, superuser_token_headers, normal_user_token_headers
# - 测试包含：模型/CRUD 基础、服务级别处理（高层流程）、以及 API 路由的集成测试
# - 保持简单、易维护，遵循项目现有测试风格


def test_create_document_via_api(client: TestClient, superuser_token_headers: dict[str, str], db: Session) -> None:
    data = {
        "title": "Test Document",
        "content": "This is a small document used for testing.",
    }
    r = client.post(
        f"{settings.API_V1_STR}/documents/",
        headers=superuser_token_headers,
        json=data,
    )
    assert 200 <= r.status_code < 300
    created = r.json()
    assert created["title"] == data["title"]
    assert "id" in created

    # 确认数据库中存在
    doc_query = select(Document).where(Document.id == created["id"])
    db_doc = db.exec(doc_query).first()
    assert db_doc is not None
    assert db_doc.title == data["title"]


def test_read_document_not_found(client: TestClient, superuser_token_headers: dict[str, str]) -> None:
    r = client.get(f"{settings.API_V1_STR}/documents/{uuid.uuid4()}", headers=superuser_token_headers)
    assert r.status_code == 404
    assert "detail" in r.json()


def test_document_crud_lifecycle(db: Session) -> None:
    # 1. 创建（CRUD 层面）
    from app.crud.document import create_document, get_document_by_id, update_document, delete_document

    doc_in = DocumentCreate(title="Crud Doc", content="Crud content")
    created = create_document(session=db, document_create=doc_in)
    assert created.id is not None
    assert created.title == "Crud Doc"

    # 2. 读取
    fetched = get_document_by_id(session=db, document_id=created.id)
    assert fetched is not None
    assert fetched.title == created.title

    # 3. 更新
    updated = update_document(session=db, document_id=created.id, document_update={"title": "Updated Crud Doc"})
    assert updated.title == "Updated Crud Doc"

    # 4. 删除
    deleted = delete_document(session=db, document_id=created.id)
    assert deleted is True

    fetched_after = get_document_by_id(session=db, document_id=created.id)
    assert fetched_after is None


def test_document_service_process_document(db: Session) -> None:
    # 测试 document 服务的关键方法（高层伪流程）
    from app.services.document.document_service import DocumentService

    service = DocumentService(db_session=db)

    # 创建文档记录（service 层）
    doc = service.create_document(title="Service Doc", content="Service content")
    assert doc is not None
    assert doc.title == "Service Doc"

    # 调用处理流程（如果实现包含分割等，至少调用接口以确保无异常）
    # 这里仅调用并断言返回类型或状态，避免对外部依赖产生副作用
    result = service.process_document(document_id=doc.id)
    # process_document 可能返回 True 或处理结果对象，根据实现做宽松判断
    assert result is not None


def test_document_route_permissions(client: TestClient, normal_user_token_headers: dict[str, str], db: Session) -> None:
    # 普通用户尝试访问需要更高权限的路由应被拒绝（示例场景）
    data = {"title": "Perm Doc", "content": "Content"}
    r = client.post(
        f"{settings.API_V1_STR}/documents/",
        headers=normal_user_token_headers,
        json=data,
    )
    # 根据项目策略，普通用户是否能创建文档取决于实现；这里采用宽松断言：要么 2xx 要么 403
    assert r.status_code in (200, 201, 403)


def test_document_indexing_task_enqueue(client: TestClient, superuser_token_headers: dict[str, str], db: Session) -> None:
    # 测试创建文档后是否会触发异步任务入队（Dramatiq）
    # 不依赖真实队列，仅调用创建接口并确保返回包含 id，同时若有标记字段也可检查
    data = {"title": "Task Doc", "content": "Task content"}
    r = client.post(f"{settings.API_V1_STR}/documents/", headers=superuser_token_headers, json=data)
    assert 200 <= r.status_code < 300
    created = r.json()
    assert "id" in created

    # 若项目在创建时返回 'task_enqueued' 标记则断言其存在（为兼容性检查）
    if "task_enqueued" in created:
        assert created["task_enqueued"] in (True, False)

