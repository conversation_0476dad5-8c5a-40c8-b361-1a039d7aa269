# services/gateway - 技术概念验证 (POC) 开发指南

基于 docs/PROJECT_BLUEPRINT.md（版本 1.1）已确定技术栈：  
FastAPI, SQLModel, Pydantic 2.5+, Uvicorn, PostgreSQL 13+, Redis 6+, Dramatiq, Manticore Search 6.0+, Docker Compose, Traefik。

**作者**: Roo  
**状态**: 草稿

---
## 目标与背景（与蓝图一致）

Gateway（BFF）负责作为前端与内部微服务之间的统一入口，处理路由、鉴权、请求聚合、速率限制与部分降级逻辑。蓝图明确选择 Traefik 作为边缘网关并用 FastAPI 做 BFF 以便保持后端微服务一致的认证与聚合策略。

---
## POC 核心目标

- 证明 Traefik 与 FastAPI BFF 的基本集成（路由、SSL、转发）
- 验证 BFF 能并发聚合多个后端服务（document, embedding, llm）并返回合并结果
- 实现简单的 JWT 鉴权中间件（与 PROJECT_BLUEPRINT 的 User Service 兼容）
- 支持 WebSocket 代理以演示实时会话（基本功能示例）

---
## 技术细节（与蓝图对齐）

- 边缘网关：Traefik（负责 TLS、入口流量管理）
- BFF：FastAPI + Uvicorn（处理业务聚合、鉴权、速率限制）
- 鉴权：JWT（与 User Service 配合）
- HTTP 客户端：httpx AsyncClient（用于内部服务聚合）
- WebSocket 转发：FastAPI WebSocket 转发到 Conversation Service（示例）
- 速率/熔断：在 BFF 层实现简单限流（可扩展到中间件或第三方）

---
## 最小 demo（POC）

- demo/gateway_poc/
  - README.md（本文件）
  - docker-compose.yml（Traefik + bff demo）
  - main.py（FastAPI BFF 示例）
  - requirements.txt

示例 docker-compose（最简，仅供 POC）：
```yaml
version: '3.8'
services:
  traefik:
    image: traefik:v2.10
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--entrypoints.web.address=:80"
    ports:
      - "80:80"
      - "8080:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro

  bff:
    build: .
    labels:
      - "traefik.http.routers.bff.rule=Host(`localhost`)"
      - "traefik.http.services.bff.loadbalancer.server.port=8000"
    ports:
      - "8000:8000"
```

---
## POC 流程示意（伪代码）

- 聚合路由示例：GET /api/v1/aggregate?topic=123
  - BFF 并发调用 document service、embedding service、llm service
  - 合并并返回统一响应

伪代码：
```python
@app.get("/api/v1/aggregate")
async def aggregate(topic_id: str):
    async with httpx.AsyncClient() as client:
        r1 = client.get(f"http://document:8000/api/v1/topics/{topic_id}/meta")
        r2 = client.get(f"http://topic:8000/api/v1/topics/{topic_id}/stats")
        r3 = client.post(f"http://llm:8000/api/v1/llm/generate", json={"prompt":"..."})
        res = await asyncio.gather(r1, r2, r3)
    return {"doc": res[0].json(), "stats": res[1].json(), "llm": res[2].json()}
```

---
## 配置与集成点（按蓝图）

- Traefik 负责对外 TLS/路由；BFF 在内部调用 services/*
- .env 示例：
  - BFF_HOST=0.0.0.0
  - BFF_PORT=8000
  - AUTH_JWT_SECRET=xxxx
  - RATE_LIMIT_PER_MIN=120
- BFF 依赖服务：
  - services/document
  - services/embedding
  - services/llm
  - services/user（鉴权）

---
## 验证清单（与蓝图对齐）

- [ ] Traefik 能路由到 BFF，BFF 能处理基础请求
- [ ] BFF 聚合多个后端接口并返回合并结果（性能与正确性）
- [ ] 鉴权中间件验证 JWT 并拒绝未授权请求
- [ ] WebSocket 请求能被代理到 Conversation Service（基本连接测试）
- [ ] 限流策略在高并发下能生效并返回标准错误码

---
## 风险与注意事项

- BFF 成为潜在瓶颈：POC 中注意响应超时与降级策略
- 认证策略需与 User Service 紧密配合以避免不一致
- Traefik 配置在生产中需配置 TLS 证书与中间件（速率限制、白名单等）

---
## 参考（项目内）

- docs/PROJECT_BLUEPRINT.md（整体蓝图）
- deployment/docker/ 与 docker-compose.yml（主 Compose 文件示例）