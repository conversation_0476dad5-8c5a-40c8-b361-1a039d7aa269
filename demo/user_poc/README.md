# services/user - 技术概念验证 (POC) 开发指南

基于 docs/PROJECT_BLUEPRINT.md（版本 1.1）已确定技术栈：  
FastAPI, SQLModel, Pydantic 2.5+, Uvicorn, PostgreSQL 13+, Redis 6+, Dramatiq, Manticore Search 6.0+, Docker Compose, Traefik。

**作者**: Roo  
**状态**: 草稿

---
## 目的与范围（按蓝图）

User Service 提供用户注册、登录、会话管理与基础权限控制（JWT）。本 POC 验证与 PROJECT_BLUEPRINT 中规定的认证/鉴权方案兼容，并能为其他服务（BFF/Gateway、Topic、Document 等）提供统一的鉴权接口。

---
## 技术细节（与蓝图一致）

- 后端：FastAPI（异步）
- 数据模型与 ORM：SQLModel（与 Pydantic 2.5+）
- 密码与安全：bcrypt（PassLib），JWT（pyjwt或jose）
- 驱动：asyncpg（PostgreSQL）
- 会话/缓存：Redis（用于短期会话缓存、速率限制等）
- 异步任务：Dramatiq（用于邮件、通知等异步工作）
- 配置：参照 PROJECT_BLUEPRINT 的配置管理策略（.env 在开发，生产通过平台注入）

---
## 最小化 demo（POC）

- demo/user_poc/
  - README.md（本文件）
  - requirements.txt
  - main.py 或 test_poc.py（实现注册/登录/受保护路由演示）
  - docker-compose.yml（可选，集成 PostgreSQL/Redis）

示例 requirements.txt（示意）:
```
fastapi
uvicorn[standard]
sqlmodel
asyncpg
passlib[bcrypt]
python-jose[cryptography]
redis
dramatiq
httpx
```

---
## POC 功能点与示例 API

- POST /api/v1/auth/register -> 创建用户（email, password）
- POST /api/v1/auth/login -> 返回 access_token (JWT)
- GET /api/v1/me -> 受保护路由，返回当前用户信息（需要 Authorization: Bearer token）
- POST /api/v1/auth/refresh -> 刷新 token（若实现）

伪代码（关键流程）：
```python
from fastapi import FastAPI, Depends, HTTPException
from backend.app.core.security import create_access_token, verify_password

@app.post("/auth/register")
async def register(payload: Register):
    hashed = hash_password(payload.password)
    user = await create_user(payload.email, hashed)
    return {"id": user.id}

@app.post("/auth/login")
async def login(payload: Login):
    user = await get_user_by_email(payload.email)
    if not verify_password(payload.password, user.hashed_password):
        raise HTTPException(status_code=401)
    token = create_access_token({"sub": user.id})
    return {"access_token": token}
```

---
## 集成点（按蓝图）

- 上游：前端（React）
- 下游：所有需鉴权的服务（BFF/Gateway, Topic, Document, Conversation）
- 配置（.env）：
```
JWT_SECRET=supersecret
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60
DATABASE_URL=postgresql+asyncpg://user:pass@postgres:5432/db
REDIS_URL=redis://redis:6379/0
```

---
## 验证清单（与蓝图 P0 对齐）

- [ ] 用户注册成功并将哈希密码存入 PostgreSQL（无明文存储）
- [ ] 登录返回有效 JWT，受保护路由能正确验证
- [ ] 刷新/撤销策略（若实现）能在简单测试中生效
- [ ] 与 Gateway 的鉴权流程兼容（BFF 能验证 token 并调用下游）

---
## 风险与注意事项

- 刷新令牌与撤销策略需谨慎设计；POC 可使用简单策略，生产需更严格
- 密码策略（复杂度、速率限制）在产品阶段需加强
- 邮件验证/密码重置为 P1 功能，POC 中可暂时跳过或模拟

---
## 参考（项目内）

- backend/app/core/security.py（项目已有安全逻辑）
- docs/PROJECT_BLUEPRINT.md（总体蓝图）