"""
POC script for services/document (minimal, runnable example)

This script is a lightweight demonstration (not a production app):
- Reads a local sample text file
- Uses the repository's engines.text_splitter API (if available) to split into chunks
- Simulates persisting chunk metadata (prints to stdout)
- Demonstrates publishing a Dramatiq task payload (prints task payload)

Notes:
- This file is a minimal POC for local experimentation and lives under demo/.
- It follows the PROJECT_BLUEPRINT.md tech choices: async-first, simple dependencies.
- Replace calls to real services (DB, Dramatiq, Embedding, Manticore client) with actual implementations when integrating.

Usage (dev):
  python demo/document_poc/main.py ./demo/document_poc/sample.txt

"""
import sys
import asyncio
import json
from pathlib import Path
from typing import List, Dict, Any

# Try to import project-internal text splitter; fallback to a naive splitter
try:
    from engines.text_splitter.engine import TextSplitterEngine
    HAS_SPLITTER = True
except Exception:
    HAS_SPLITTER = False

SAMPLE_CHUNK_MAX_TOKENS = 512

def naive_split_text(text: str, max_chars: int = 2000) -> List[Dict[str, Any]]:
    """Very small fallback splitter: split by paragraph or by length."""
    paragraphs = [p.strip() for p in text.split("\n\n") if p.strip()]
    chunks = []
    idx = 0
    for p in paragraphs:
        if len(p) <= max_chars:
            chunks.append({"index": idx, "content": p})
            idx += 1
        else:
            # naive window split
            start = 0
            while start < len(p):
                part = p[start:start+max_chars]
                chunks.append({"index": idx, "content": part})
                idx += 1
                start += max_chars
    return chunks

async def split_text(text: str) -> List[Dict[str, Any]]:
    if HAS_SPLITTER:
        engine = TextSplitterEngine({"max_tokens": SAMPLE_CHUNK_MAX_TOKENS})
        chunks = engine.split_text(text, strategy="token_based")
        # normalize to dicts
        return [{"index": i, "content": c.content} for i, c in enumerate(chunks)]
    else:
        # fallback
        return naive_split_text(text, max_chars=1500)

def persist_chunks_simulated(doc_id: str, chunks: List[Dict[str, Any]]):
    """
    Simulate persisting chunk metadata to PostgreSQL (SQLModel).
    For POC we just print a JSON summary.
    """
    records = []
    for c in chunks:
        rec = {
            "id": f"{doc_id}_{c['index']}",
            "doc_id": doc_id,
            "chunk_index": c["index"],
            "content_preview": c["content"][:200]
        }
        records.append(rec)
    print("SIMULATED PERSISTED CHUNKS:")
    print(json.dumps(records, indent=2, ensure_ascii=False))

def publish_embedding_task_simulated(chunk_ids: List[str]):
    """
    Simulate publishing a Dramatiq task to produce embeddings and index to Manticore.
    For POC we print the task payload that would be enqueued.
    """
    payload = {"task": "embed_and_index_chunks", "chunk_ids": chunk_ids}
    print("SIMULATED PUBLISH DRAMATIQ TASK:")
    print(json.dumps(payload, indent=2, ensure_ascii=False))

async def main(argv):
    if len(argv) < 2:
        print("Usage: python demo/document_poc/main.py <path-to-text-file>")
        return 1
    path = Path(argv[1])
    if not path.exists():
        print("File not found:", path)
        return 2
    text = path.read_text(encoding="utf-8")
    print(f"Loaded {len(text)} characters from {path}")

    chunks = await split_text(text)
    print(f"Generated {len(chunks)} chunks")

    # Simulate persisting metadata
    doc_id = path.stem
    persist_chunks_simulated(doc_id, chunks)

    # Simulate publishing embedding/index task
    chunk_ids = [f"{doc_id}_{c['index']}" for c in chunks]
    publish_embedding_task_simulated(chunk_ids)

    return 0

if __name__ == "__main__":
    asyncio.run(main(sys.argv))