# services/embedding - 技术概念验证 (POC) 开发指南

基于 docs/PROJECT_BLUEPRINT.md（版本 1.1）已确定技术栈：
FastAPI, SQLModel, Pydantic 2.5+, Uvicorn, PostgreSQL 13+, Redis 6+, Dramatiq, Manticore Search 6.0+, Docker Compose, Traefik。

**作者**: Roo  
**状态**: 草稿

---
## 核心原则：遵循 PROJECT_BLUEPRINT 的“可插拔、异步优先、单人可维护”原则

Embedding Service 负责将 document service 输出的 chunk 转为向量表示（embedding），并提供批量、异步与实时的向量化接口。本 POC 验证本服务与 Manticore 索引写入流水线的对接能力。

---
## 目标与成功标准（与蓝图一致）

- 支持本地模型（sentence-transformers）与远端 API（OpenAI）两种模式，接口保持一致。
- 提供异步批量接口以支持高吞吐（Dramatiq 任务触发）。
- 成功将 embedding 发送至索引写入流（Dramatiq -> Manticore）。
- 成功验证向量维度一致性（PROJECT_BLUEPRINT 强制 EMBEDDING_DIM 全局统一）。

成功标准：
- 本地批量 1000 chunks 处理延迟在可接受范围内（以机器为准，POC 验证吞吐）。
- Embedding 维度与 Manticore 字段配置一致且检索召回正确率满足基本标准。

---
## 技术细节（与蓝图对齐）

- 服务框架：FastAPI (async)
- 模型调用：
  - local: sentence-transformers (推荐用于离线/批量)
  - remote: OpenAI embedding API（按需）
- 批量/异步：
  - Dramatiq 用于处理长批量任务，Redis 作为 broker
  - 支持 batch_size、worker_count 配置
- 输出与约束：
  - EMBEDDING_DIM 在 .env 中配置（与 Manticore 对齐）
  - 结果通过 JSON 返回：{ "id": "...", "vector": [...] }

---
## 最小化 demo 文件结构

- demo/embedding_poc/
  - README.md（本文件）
  - requirements.txt
  - main.py / test_poc.py（示例：读取 chunk 列表 -> 批量 embed -> 输出向量文件）
  - docker-compose.yml（可选，与 manticore/demo 集成时使用）

示例 requirements.txt（示意）:
```
fastapi
uvicorn[standard]
sentence-transformers
transformers
torch   # 或者根据模型选择适配后端
aiohttp
dramatiq
redis
requests
```

---
## POC 核心流程（伪代码）

1. 从 Document Service 或本地文件读取 chunks
2. 按 batch_size 批量发送到模型（local call 或 remote API）
3. 对输出进行必要的归一化（cosine 兼容）
4. 将 embedding 与 chunk id 发送到 Dramatiq 任务以写入 Manticore

伪代码：
```python
from sentence_transformers import SentenceTransformer
model = SentenceTransformer("all-MiniLM-L6-v2")  # 示例

def embed_batch(texts):
    vecs = model.encode(texts, convert_to_numpy=True, batch_size=64)
    # 归一化（若需要）
    return vecs
```

---
## 集成点与配置（与蓝图一致）

- 上游：services/document（chunks）
- 下游：Dramatiq Worker -> 写入 Manticore（docs_chunks）
- 配置（.env）：
  - EMBEDDING_MODEL_NAME=all-MiniLM-L6-v2
  - EMBEDDING_BATCH_SIZE=64
  - EMBEDDING_WORKER_COUNT=2
  - EMBEDDING_SERVICE_URL=http://embedding:8000/embed
  - EMBEDDING_DIM=384

---
## 验证清单（与蓝图对齐）

- [ ] 本地模型能批量生成向量且多次运行结果一致（小幅浮点差异允许）
- [ ] Embedding 服务能通过 HTTP 接口接收 chunks 并返回 vectors
- [ ] Dramatiq worker 能消费 embedding 任务并调用 Manticore 写入
- [ ] Embedding 向量维度与 Manticore 字段一致并可用于向量检索

---
## 风险与注意事项（与蓝图一致）

- 本地 GPU/CPU 资源限制：POC 在目标机器上测试吞吐；生产建议提供 GPU 或使用远端API备份
- 模型替换策略：为避免服务中断，提供“local -> remote”无缝切换机制
- 向量精度与归一化需文档化为系统约定

---
## 参考（项目内）

- demo/document_poc/README.md（document -> embedding 流程）
- docs/PROJECT_BLUEPRINT.md（整体技术栈与集成指导）