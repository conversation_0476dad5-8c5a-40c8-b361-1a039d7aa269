# Demo 开发顺序与并行分组（基于 PROJECT_BLUEPRINT.md）

说明：此清单按照依赖关系和最小可验证路径排列。优先保证核心数据流（Document -> Embedding -> Manticore）可端到端工作，然后并行推进其余服务。每一组内部的任务可并行开发；不同组之间存在依赖关系请按顺序执行。

---

## 总原则
- 按 PROJECT_BLUEPRINT 已确定技术栈实现（FastAPI / SQLModel / Pydantic 2.5+ / PostgreSQL / Redis / Dramatiq / Manticore / Docker Compose / Traefik）。
- 优先保证“文档 -> 切片 -> 向量化 -> 索引 -> 检索”的核心流水线可运行并可被 LLM 调用。
- 每个 demo 目录要包含：README.md、requirements.txt、main.py（或 test_poc.py）。先生成文件，后执行运行验证（运行需额外许可）。

---

## 推荐顺序（阶段与并行组）

阶段 0 — 环境准备（并行）
- A. 基础 infra（仅配置示例）
  - 生成统一 .env 模板（包含 DATABASE_URL, REDIS_URL, MANTICORE_HOST/PORT, EMBEDDING_DIM 等）
  - 统一 Docker Compose 示意文件（用于本地 POC）
- B. 共用库适配（并行）
  - engines/text_splitter 确认（项目内复用）
  - 异步 manticore client 封装（若已有则复用，不存在时用 minimal wrapper）

阶段 1 — 核心流水线（顺序，关键路径）
- 1. Document Service POC（必须先实现）
  - 目标：上传文件 -> text_splitter -> 写入 chunk metadata -> publish Dramatiq 任务
  - 产物：demo/document_poc/main.py + requirements.txt
- 2. Embedding Service POC（可在 Document Service 有稳定输出后并行）
  - 目标：接收 chunk -> 生成 embedding（local 或 remote）-> 将 embedding 交付写入队列
  - 产物：demo/embedding_poc/main.py + requirements.txt
- 3. Manticore Integration POC（紧接 Embedding）
  - 目标：将 chunk + embedding 写入 Manticore，验证关键词/向量混合检索
  - 产物：demo/manticore_poc/README.md (已有) + demo/manticore_poc 示例脚本（如需）

阶段 2 — LLM 与对话（可以与阶段1 后半并行，但依赖检索锚点）
- 4. LLM Integration POC
  - 目标：从 Manticore 检索长期记忆锚点 -> 拼接 prompt -> 调用 LLM（local 或 OpenAI） -> 返回
  - 产物：demo/llm_poc/main.py + requirements.txt

阶段 3 — 辅助服务（可高度并行）
- 5. Gateway (BFF) POC
  - 目标：Traefik + FastAPI 聚合后端接口、JWT 鉴权示例
  - 产物：demo/gateway_poc/main.py + requirements.txt
- 6. Topic Service POC
  - 目标：Topic CRUD 与与 Manticore 的主题检索集成
  - 产物：demo/topic_poc/main.py + requirements.txt
- 7. User Service POC
  - 目标：注册/登录/受保护路由（JWT）
  - 产物：demo/user_poc/main.py + requirements.txt

阶段 4 — 验证与整合
- 8. 端到端 POC 验证（需用户授权运行）
  - 运行顺序：启动 PostgreSQL, Redis, Manticore -> 启动 Document -> Embedding -> Workers -> 写入并检索 -> LLM 调用验证
- 9. 填写 deepwiki 校验与文档最终确认（并行，可与整合阶段并行）

---

## 并行策略（可同时进行的任务组）
- 并行组 1（早期并行）：
  - A. engines/text_splitter 校验/微调
  - B. manticore client 的异步封装
  - C. 基础 .env 与 Docker Compose 模板准备
- 并行组 2（核心功能并行，需 Document 输出接口稳定）：
  - Document Service（处理/发布 chunk）
  - Embedding Service（消费 chunk -> 生成向量）
  - Manticore 写入 Worker（可在 Embedding 输出稳定后并行）
- 并行组 3（非强依赖，可并行）：
  - Gateway (BFF)
  - Topic Service
  - User Service
  - LLM Integration（需要检索接口，但可用 mock 数据并行开发）
- 并行组 4（完成阶段）：
  - CI 测试脚本、示例脚本完善、撰写运行/验证说明

---

## 简短开发时间估算（POC 粒度）
- Document POC: 1–2 天
- Embedding POC: 1–2 天
- Manticore POC: 0.5–1 天（取决于索引建模难度）
- LLM Integration POC: 1–2 天（含 prompt 管理）
- Gateway/Topic/User POC（每项）: 0.5–1 天（基础功能）
- 端到端验证 + 文档：1–2 天

---

## 建议的具体下一步（我将帮你执行）
1. 我会按并行组 1 与核心流水线优先级在 demo/ 下创建剩余示例脚本与 requirements.txt（你已授权我创建文件）。
2. 创建完成后我会更新 TODO 列表并提交文件路径供你审阅。
3. 运行验证需要你授权；否则我会停在“已创建文件”步骤等待你批准运行。
