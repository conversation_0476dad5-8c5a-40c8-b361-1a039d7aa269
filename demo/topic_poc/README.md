# services/topic - 技术概念验证 (POC) 开发指南

基于 docs/PROJECT_BLUEPRINT.md（版本 1.1）已确定技术栈：  
FastAPI, SQLModel, Pydantic 2.5+, Uvicorn, PostgreSQL 13+, Redis 6+, Dramatiq, Manticore Search 6.0+, Docker Compose, Traefik。

**作者**: Roo  
**状态**: 草稿

---
## 说明（遵循蓝图）

Topic Service 负责管理用户的“学习主题”，将文档、对话与摘要按主题组织，并为 Conversation Service 与检索引擎（Manticore）提供主题级别的长期记忆锚点。本 POC 按 PROJECT_BLUEPRINT 指定的技术栈实现最小功能验证。

---
## 目标与成功标准

- 实现主题的 CRUD API（创建、读取、更新、删除）
- 支持将 document_id 与 conversation_id 关联到 topic
- 提供按主题检索历史摘要与长期记忆锚点（从 Manticore 检索）
- 成功通过 JWT 鉴权与 User Service 协作进行权限验证

成功标准：
- 能创建主题并关联至少一个文档与一段对话
- 能从 Manticore 检索与主题相关的摘要锚点并返回给调用方

---
## 技术细节（与蓝图一致）

- 框架：FastAPI（异步）
- 数据模型：SQLModel（PostgreSQL via asyncpg）
- 缓存/会话：Redis（用于会话与短期缓存）
- 异步任务：Dramatiq（如需要异步同步到 Manticore 的索引事件）
- 长期记忆检索：Manticore Search（通过配置的 MANTICORE_HOST/PORT 调用）
- 鉴权：与 User Service（PROJECT_BLUEPRINT 已定义）通过 JWT 协作

---
## 最小化 demo 内容

- demo/topic_poc/
  - README.md（本文件）
  - requirements.txt
  - main.py（演示创建主题、关联文档、从 Manticore 检索锚点）
  - docker-compose.yml（用于本地验证，集成 PostgreSQL/Redis/Manticore）

示例 requirements.txt（示意）:
```
fastapi
uvicorn[standard]
sqlmodel
asyncpg
httpx
redis
dramatiq
pydantic>=2.5
```

---
## POC 流程（伪代码）

1. POST /api/v1/topics -> 创建主题，返回 topic_id
2. POST /api/v1/topics/{topic_id}/documents -> 关联 document_id（由 Document Service 提供）
3. GET /api/v1/topics/{topic_id}/memory -> 调用 Manticore 检索主题长期记忆锚点并返回
4. 权限：仅主题 owner 或被授权用户可编辑/查看（基于 User Service 的 JWT）

伪代码：
```python
@app.post("/api/v1/topics")
async def create_topic(payload: TopicCreate):
    topic = TopicModel.create(payload)
    return topic

@app.post("/api/v1/topics/{topic_id}/documents")
async def attach_document(topic_id: str, doc: AttachDoc):
    add_doc_to_topic(topic_id, doc.doc_id)
    return {"ok": True}

@app.get("/api/v1/topics/{topic_id}/memory")
async def topic_memory(topic_id: str, limit: int = 5):
    snippets = await manticore_client.search(index="docs_chunks", query={"match": {"metadata.topic_id": topic_id}}, limit=limit)
    return snippets
```

---
## 集成与配置（与蓝图对齐）

- 必要配置（.env）：
  - DATABASE_URL=postgresql+asyncpg://user:pass@postgres:5432/db
  - REDIS_URL=redis://redis:6379/0
  - MANTICORE_HOST=manticore
  - MANTICORE_HTTP_PORT=9308
- 上游：Document Service（提供 document_id）、User Service（鉴权）
- 下游：Conversation Service（为主题提供会话关联）

---
## 验证清单（与蓝图对齐）

- [ ] 创建主题 API 工作正常并写入 PostgreSQL
- [ ] 主题可关联 document_id 与 conversation_id 并在检索中体现
- [ ] GET /topics/{id}/memory 能返回来自 Manticore 的锚点（至少 1 条）
- [ ] 权限验证（仅 owner 或被授权用户可编辑）

---
## 风险与注意事项

- 权限设计需与 User Service 统一，避免重复实现
- 主题删除/转移的级联清理需设计（删除主题时如何处理索引/关联）
- Manticore 查询语句需加入 topic_id 过滤以保证召回相关性

---
## 参考（项目内）

- backend/app/crud/topic.py
- backend/app/models/topic.py
- docs/PROJECT_BLUEPRINT.md（总体蓝图）