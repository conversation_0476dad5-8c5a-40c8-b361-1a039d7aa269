# services/manticore - 技术概念验证 (POC) 开发指南

基于 docs/PROJECT_BLUEPRINT.md（版本 1.1）已确定技术栈：
FastAPI, SQLModel, Pydantic 2.5+, Uvicorn, PostgreSQL 13+, Redis 6+, Dramatiq, Manticore Search 6.0+, Docker Compose, Traefik。

**作者**: Roo  
**状态**: 草稿

---
## 核心原则：按 PROJECT_BLUEPRINT 实现并验证 Manticore 集成

本 POC 遵循“研究-验证-集成”流程，目标是证明 Manticore Search（混合全文+向量）在本项目中作为长期记忆与上下文检索层的可行性与工程实践方式。所有决策与配置遵循 PROJECT_BLUEPRINT.md 中已确定的技术栈与约束。

---
## 阶段一：问题定义与目标（与蓝图一致）

- 要解决的问题：为主题式学习提供长期记忆检索能力，要求支持全文与向量混合检索，低运维成本，能与 FastAPI 后端和 Embedding 服务顺利集成。
- 目标产出（POC 成功标准）：
  - 在 Manticore 中创建 `docs_chunks` 索引并能通过 SQL 或 HTTP API 检索到文本 chunk。
  - 能将来自 `services/document` 的 chunk 写入 Manticore（包含 embedding 字段）。
  - 能基于 embedding 与关键词做混合检索并返回可用锚点供 LLM 拼接 Prompt 使用。

---
## 阶段二：技术细节（与蓝图对齐）

- 技术栈（必选）：
  - 后端框架：FastAPI（异步）
  - 数据模型/ORM：SQLModel（Pydantic + SQLAlchemy）
  - 验证与类型：Pydantic 2.5+
  - 向量/搜索引擎：Manticore Search 6.0+（全文 + KNN 支持）
  - Embedding：独立的 Embedding 服务（示例使用 sentence-transformers），与项目通过 HTTP/async 接入
  - 后台队列：Dramatiq (Redis broker)
  - 缓存/消息：Redis 6+
  - 数据库：PostgreSQL 13+
  - 容器与网关：Docker Compose + Traefik（作为边缘网关）
  - Python 异步驱动：asyncpg（Postgres）、manticore-async 或项目内封装的异步客户端

---
## 阶段三：最小化环境（PROJECT_BLUEPRINT 参考）

- 最小 demo 目录结构：
  - demo/manticore_poc/
    - README.md（本文件）
    - docker-compose.yml（最小化，仅含 manticore 服务）
    - requirements.txt（包含 manticore 客户端示例依赖）
    - main.py / test_poc.py（连接、建表、插入、检索示例）

示例最小 docker-compose（遵循蓝图：只展示 Manticore 服务，集成时在总 compose 中加入 PostgreSQL/Redis 等服务）：
```yaml
version: '3.8'
services:
  manticore:
    image: manticoresearch/manticore:latest
    environment:
      # 可根据需要通过 .env 注入 MANTICORE_SQL_PORT / MANTICORE_HTTP_PORT 等
    ports:
      - "9306:9306"  # MySQL protocol (SphinxQL)
      - "9308:9308"  # HTTP JSON API
    volumes:
      - ./manticore_data:/var/lib/manticore
```

---
## 阶段四：POC 核心脚本（伪代码 / 操作步骤）

1. 启动 Manticore: `docker compose up -d manticore`
2. 使用异步 Python 客户端连接（遵循 PROJECT_BLUEPRINT 中“异步驱动”原则）
3. 建表（示例，使用 SphinxQL 或 HTTP JSON API 创建 RT/表或索引）
4. 插入 2-3 条示例 chunk（含 embedding 字段）
5. 运行关键词与向量混合检索验证召回
6. 验证幂等性（替换/replace 操作）

伪代码示例（关键流程）：
```python
# 伪代码：连接、创建表、insert/replace、search
from manticore_async_client import ManticoreClient  # 项目内或社区客户端封装
client = ManticoreClient(host="127.0.0.1", port=9308, protocol="http")

# 1. 创建索引/表（示例语句，按 Manticore 版本调整）
await client.sql("DROP TABLE IF EXISTS docs_chunks;")
await client.sql("""
CREATE TABLE docs_chunks (
  id VARCHAR,
  doc_id VARCHAR,
  content TEXT,
  metadata JSON,
  embedding VECTOR(384)
);
""")

# 2. 插入数据（replace/upsert）
await client.replace(index="docs_chunks", doc={
  "id": "d1_0", "doc_id": "d1", "content": "示例内容", "metadata": {"source":"demo"}, "embedding": vec
})

# 3. 搜索验证（关键词）
res = await client.search(index="docs_chunks", query={"match": {"content": "知识"}})

# 4. 向量搜索（伪代码）
res_vec = await client.search_by_vector(index="docs_chunks", vector=vec, top_k=5)
```

---
## 阶段五：集成点（与蓝图一致）

- 上游数据：由 Document Service 提供 chunk（TextSplitter 输出）
- 向量：Embedding Service 提供向量（异步 HTTP），Embedding dim 需在系统配置中统一（PROJECT_BLUEPRINT 建议统一 EMBEDDING_DIM）
- 写入：POC 使用 replace/upsert 写入 Manticore（保证幂等）
- 检索：Conversation/LLM 服务调用 Manticore 获取长期记忆锚点
- 配置：在 .env 中统一注入 MANTICORE_HOST, MANTICORE_SQL_PORT, MANTICORE_HTTP_PORT, MANTICORE_EMBEDDING_DIM

示例 .env 条目（示意）：
```
MANTICORE_HOST=127.0.0.1
MANTICORE_SQL_PORT=9306
MANTICORE_HTTP_PORT=9308
MANTICORE_EMBEDDING_DIM=384
```

---
## 验证清单（与蓝图 P0/P1 要求对齐）

- [ ] Manticore 服务能在 Docker Compose 环境中启动并响应端口（9306/9308）
- [ ] 能成功通过异步客户端连接并创建索引/表
- [ ] 能成功写入并检索关键词召回（中文与英文示例均验证）
- [ ] 能写入向量并执行向量相似性检索（top-k）
- [ ] 幂等性验证：重复 replace 不产生重复条目
- [ ] 集成文档：记录关键 .env 配置项与运行步骤（与 PROJECT_BLUEPRINT 的配置策略一致）

---
## 风险与注意事项（按蓝图）

- 确保 Embedding 服务输出维度与 Manticore 向量字段配置一致（PROJECT_BLUEPRINT 强调 embedding_dim 统一）
- 在高并发写入场景下需要通过 Dramatiq 批量写入以减小写入压力（将写入任务排入后台任务队列）
- 生产环境请考虑 Manticore 的备份/恢复与权限管理（蓝图后续阶段：运维/备份）
- 在 CI/CD 中加入 POC 的最小端到端测试，确保 Manticore 集成在模板生成的新仓库能顺利工作

---
## 下一步（按你同意的执行计划）

1. 我将把上述蓝图对齐的内容同步写入其他 demo/*_poc/README.md（document, embedding, llm, gateway, topic, user）以及 docs/2_Architecture/MANTICORE_DETAILED.md（已部分存在），把所有技术栈、示例 docker-compose、配置项和验证清单按 PROJECT_BLUEPRINT 完全替换。  
2. 写入完成后我会更新 TODO 列表并列出已修改文件路径供你审阅。  
3. 若你同意，我会继续创建每个 demo 的示例脚本与 requirements.txt（下一步骤）。
